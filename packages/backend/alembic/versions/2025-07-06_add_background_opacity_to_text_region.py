"""add_background_opacity_to_text_region

Revision ID: 3fe1ca5046c5
Revises: 1d185b05d524
Create Date: 2025-07-06 11:35:28.038030

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3fe1ca5046c5'
down_revision = '1d185b05d524'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('text_region', sa.Column(
        'background_opacity', sa.Float(), nullable=True))
    # Note: SQLite doesn't support ALTER COLUMN, so we skip the background_color length change
    # The existing column can already handle 'transparent' (11 chars) even with VARCHAR(7) definition
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('text_region', 'background_color',
                    existing_type=sa.String(length=11),
                    type_=sa.VARCHAR(length=7),
                    existing_nullable=True)
    op.drop_column('text_region', 'background_opacity')
    # ### end Alembic commands ###
