'use client';

import { useState, useRef, useCallback } from 'react';
import {
  Settings,
  Eye,
  Languages,
  Layers,
  Type,
  Palette
} from 'lucide-react';
import { useEditor } from './EditorContext';

export default function RightSidebar() {
  const { state, dispatch } = useEditor();
  const [activeTab, setActiveTab] = useState<'properties' | 'ocr' | 'translation' | 'layers'>('properties');

  // Live color preview state
  const [livePreview, setLivePreview] = useState<{
    font_color?: string;
    background_color?: string;
  }>({});
  const isColorDragging = useRef(false);
  const pendingColorUpdate = useRef<{
    font_color?: string;
    background_color?: string;
    background_opacity?: number;
  }>({});

  // Get selected regions data
  const selectedRegions = state.textRegions.filter(region =>
    state.selectedRegions.includes(region.id)
  );

  // Get current styles (for single selection or common styles)
  const getCurrentStyles = () => {
    if (selectedRegions.length === 0) {
      return {
        font_family: 'Arial',
        font_size: 14,
        font_color: '#000000',
        background_color: 'transparent',
        background_opacity: 1.0
      };
    }

    if (selectedRegions.length === 1) {
      return {
        ...selectedRegions[0],
        background_opacity: selectedRegions[0].background_opacity ?? 1.0
      };
    }

    // For multi-region selection, find common styles
    const firstRegion = selectedRegions[0];
    const commonStyles = {
      font_family: firstRegion.font_family,
      font_size: firstRegion.font_size,
      font_color: firstRegion.font_color,
      background_color: firstRegion.background_color,
      background_opacity: firstRegion.background_opacity ?? 1.0
    };

    // Check if all regions have the same styles
    for (let i = 1; i < selectedRegions.length; i++) {
      const region = selectedRegions[i];
      if (region.font_family !== commonStyles.font_family) commonStyles.font_family = 'mixed';
      if (region.font_size !== commonStyles.font_size) commonStyles.font_size = 0;
      if (region.font_color !== commonStyles.font_color) commonStyles.font_color = 'mixed';
      if (region.background_color !== commonStyles.background_color) commonStyles.background_color = 'mixed';
      if ((region.background_opacity ?? 1.0) !== commonStyles.background_opacity) commonStyles.background_opacity = -1; // Use -1 to indicate mixed
    }

    return commonStyles;
  };

  const currentStyles = getCurrentStyles();

  // Apply live preview colors to current styles
  const displayStyles = {
    ...currentStyles,
    ...(livePreview.font_color && { font_color: livePreview.font_color }),
    ...(livePreview.background_color && { background_color: livePreview.background_color })
  };

  // Handle live color preview
  const handleLiveColorChange = useCallback((colorType: 'font_color' | 'background_color', color: string) => {
    if (state.selectedRegions.length === 0) return;

    // Start color dragging mode
    isColorDragging.current = true;

    // Update live preview
    setLivePreview(prev => ({
      ...prev,
      [colorType]: color
    }));

    // Store pending update
    pendingColorUpdate.current = {
      ...pendingColorUpdate.current,
      [colorType]: color
    };

    // Apply live preview to DOM elements directly
    applyLiveColorPreview(colorType, color);
  }, [state.selectedRegions]);



  // Apply live color preview via custom events
  const applyLiveColorPreview = useCallback((colorType: 'font_color' | 'background_color', color: string) => {
    // Dispatch custom event to notify TextRegion components
    const event = new CustomEvent('live-color-preview', {
      detail: {
        regionIds: state.selectedRegions,
        colorType,
        color
      }
    });
    window.dispatchEvent(event);
  }, [state.selectedRegions]);



  // Handle mouse up to commit color changes
  const handleColorMouseUp = useCallback(() => {
    if (!isColorDragging.current) return;

    // Reset dragging state
    isColorDragging.current = false;

    // Dispatch event to clear live preview in TextRegion components
    window.dispatchEvent(new CustomEvent('color-preview-end'));

    // Apply pending updates to React state
    if (Object.keys(pendingColorUpdate.current).length > 0) {
      handleStyleUpdate(pendingColorUpdate.current);
      pendingColorUpdate.current = {};
    }

    // Clear live preview
    setLivePreview({});
  }, []);

  // Handle style updates with validation
  const handleStyleUpdate = (styles: Partial<{ font_family: string; font_size: number; font_color: string; background_color: string; background_opacity: number }>) => {
    console.log('handleStyleUpdate called with:', styles, 'selectedRegions:', state.selectedRegions);
    if (state.selectedRegions.length === 0) return;

    // Validate inputs
    const validatedStyles: typeof styles = {};

    if (styles.font_family !== undefined) {
      validatedStyles.font_family = styles.font_family;
    }

    if (styles.font_size !== undefined) {
      const size = Math.max(8, Math.min(72, styles.font_size));
      validatedStyles.font_size = size;
    }

    if (styles.font_color !== undefined) {
      // Basic hex color validation
      const color = styles.font_color;
      if (color === '' || color === 'mixed' || /^#[0-9A-F]{6}$/i.test(color) || /^#[0-9A-F]{3}$/i.test(color)) {
        validatedStyles.font_color = color || '#000000';
      }
    }

    if (styles.background_color !== undefined) {
      const color = styles.background_color;
      if (color === 'transparent' || color === 'mixed' || color === '' || /^#[0-9A-F]{6}$/i.test(color) || /^#[0-9A-F]{3}$/i.test(color)) {
        validatedStyles.background_color = color || 'transparent';
      }
    }

    if (styles.background_opacity !== undefined) {
      const opacity = Math.max(0, Math.min(1, styles.background_opacity));
      validatedStyles.background_opacity = opacity;
    }

    // Only dispatch if we have valid styles to update
    if (Object.keys(validatedStyles).length > 0) {
      dispatch({
        type: 'UPDATE_TEXT_REGION_STYLES',
        payload: {
          regionIds: state.selectedRegions,
          styles: validatedStyles
        }
      });
    }
  };

  return (
    <div className="h-full bg-white border-l border-gray-200 flex flex-col">
      {/* Sidebar Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Properties</h2>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200">
        {[
          { id: 'properties', label: 'Props', icon: Settings },
          { id: 'ocr', label: 'OCR', icon: Eye },
          { id: 'translation', label: 'Trans', icon: Languages },
          { id: 'layers', label: 'Layers', icon: Layers },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 px-2 py-2 text-sm font-medium border-b-2 transition-colors ${activeTab === tab.id
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
          >
            <tab.icon className="w-4 h-4 mx-auto mb-1" />
            <div className="text-xs">{tab.label}</div>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'properties' && (
          <div className="p-4 space-y-4">
            {state.selectedRegions.length > 0 ? (
              <>
                {/* Text Region Properties */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Text Region</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        Type
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                        <option>Speech Bubble</option>
                        <option>Thought Bubble</option>
                        <option>Narration</option>
                        <option>Sound Effect</option>
                        <option>Sign</option>
                        <option>Other</option>
                      </select>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          X
                        </label>
                        <input
                          type="number"
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          placeholder="0"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          Y
                        </label>
                        <input
                          type="number"
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          placeholder="0"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          Width
                        </label>
                        <input
                          type="number"
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          placeholder="100"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          Height
                        </label>
                        <input
                          type="number"
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          placeholder="50"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Typography */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                    <Type className="w-4 h-4 mr-2" />
                    Typography
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        Font Family
                      </label>
                      <select
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                        value={currentStyles.font_family === 'mixed' ? '' : currentStyles.font_family}
                        onChange={(e) => handleStyleUpdate({ font_family: e.target.value })}
                      >
                        {currentStyles.font_family === 'mixed' && (
                          <option value="" disabled>Mixed</option>
                        )}
                        <option value="Arial">Arial</option>
                        <option value="Helvetica">Helvetica</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Comic Sans MS">Comic Sans MS</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        Font Size
                      </label>
                      <input
                        type="number"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                        value={currentStyles.font_size === 0 ? '' : currentStyles.font_size}
                        placeholder={currentStyles.font_size === 0 ? 'Mixed' : '14'}
                        onChange={(e) => handleStyleUpdate({ font_size: parseInt(e.target.value) || 14 })}
                        min="8"
                        max="72"
                      />
                    </div>

                    <div>
                      <label className="flex items-center text-xs font-medium text-gray-600 mb-1">
                        <Palette className="w-3 h-3 mr-1" />
                        Text Color
                      </label>
                      <div className="flex gap-2">
                        <input
                          type="color"
                          className="w-8 h-8 border border-gray-300 rounded"
                          value={displayStyles.font_color === 'mixed' ? '#000000' : displayStyles.font_color}
                          onChange={(e) => handleLiveColorChange('font_color', e.target.value)}
                          onMouseUp={handleColorMouseUp}
                        />
                        <input
                          type="text"
                          className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                          value={displayStyles.font_color === 'mixed' ? '' : displayStyles.font_color}
                          placeholder={displayStyles.font_color === 'mixed' ? 'Mixed' : '#000000'}
                          onChange={(e) => {
                            if (isColorDragging.current) {
                              handleLiveColorChange('font_color', e.target.value);
                            } else {
                              handleStyleUpdate({ font_color: e.target.value });
                            }
                          }}
                          onBlur={() => {
                            if (isColorDragging.current) {
                              handleColorMouseUp();
                            }
                          }}
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        Background
                      </label>
                      <div className="flex gap-2">
                        <input
                          type="color"
                          className="w-8 h-8 border border-gray-300 rounded"
                          value={displayStyles.background_color === 'mixed' ? '#ffffff' :
                            displayStyles.background_color === 'transparent' ? '#ffffff' : displayStyles.background_color}
                          onChange={(e) => handleLiveColorChange('background_color', e.target.value)}
                          onMouseUp={handleColorMouseUp}
                        />
                        <input
                          type="text"
                          className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                          value={displayStyles.background_color === 'mixed' ? '' : displayStyles.background_color}
                          placeholder={displayStyles.background_color === 'mixed' ? 'Mixed' : 'transparent'}
                          onChange={(e) => {
                            if (isColorDragging.current) {
                              handleLiveColorChange('background_color', e.target.value);
                            } else {
                              handleStyleUpdate({ background_color: e.target.value });
                            }
                          }}
                          onBlur={() => {
                            if (isColorDragging.current) {
                              handleColorMouseUp();
                            }
                          }}
                        />
                      </div>
                    </div>

                    {/* Transparency Controls */}
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-2">
                        Transparency
                      </label>

                      {/* Transparency Toggle */}
                      <div className="flex gap-2 mb-2">
                        <button
                          className={`px-3 py-1 text-xs rounded transition-colors ${displayStyles.background_color === 'transparent'
                            ? 'bg-blue-100 text-blue-700 border border-blue-300'
                            : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
                            }`}
                          onClick={() => {
                            console.log('Transparent clicked, current displayStyles.background_color:', displayStyles.background_color);
                            handleStyleUpdate({ background_color: 'transparent' });
                          }}
                        >
                          Transparent
                        </button>
                        <button
                          className={`px-3 py-1 text-xs rounded transition-colors ${displayStyles.background_color !== 'transparent' && displayStyles.background_color !== 'mixed'
                            ? 'bg-blue-100 text-blue-700 border border-blue-300'
                            : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
                            }`}
                          onClick={() => {
                            console.log('Opaque clicked, current displayStyles.background_color:', displayStyles.background_color);
                            handleStyleUpdate({ background_color: '#ffffff' });
                          }}
                        >
                          Opaque
                        </button>
                      </div>


                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <Settings className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Select a text region to edit properties</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'ocr' && (
          <div className="p-4">
            <div className="text-center text-gray-500 py-8">
              <Eye className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">OCR results will appear here</p>
            </div>
          </div>
        )}

        {activeTab === 'translation' && (
          <div className="p-4">
            <div className="text-center text-gray-500 py-8">
              <Languages className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Translation options will appear here</p>
            </div>
          </div>
        )}

        {activeTab === 'layers' && (
          <div className="p-4">
            <div className="text-center text-gray-500 py-8">
              <Layers className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Layer management coming soon</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
