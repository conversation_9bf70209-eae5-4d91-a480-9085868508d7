'use client';

import { useRef, useEffect, useState } from 'react';
import { Rect, Text, Group, Transformer } from 'react-konva';
import { useEditor } from './EditorContext';
import { TextRegionResponse, TextRegionType } from '@/types/api';
import InlineTextEditor from './InlineTextEditor';

interface TextRegionProps {
  region: TextRegionResponse;
  isSelected: boolean;
  onSelect: (e?: any) => void;
  onTransform: (updates: Partial<TextRegionResponse>) => void;
}

export default function TextRegion({ region, isSelected, onSelect, onTransform }: TextRegionProps) {
  const { state, dispatch } = useEditor();
  const groupRef = useRef<any>(null);
  const transformerRef = useRef<any>(null);
  const textRef = useRef<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [liveColors, setLiveColors] = useState<{
    font_color?: string;
    background_color?: string;
  }>({});


  // Update transformer when selection changes
  useEffect(() => {
    if (isSelected && transformerRef.current && groupRef.current) {
      transformerRef.current.nodes([groupRef.current]);
      transformerRef.current.getLayer()?.batchDraw();
    }
  }, [isSelected]);

  // Cleanup editing state on unmount
  useEffect(() => {
    return () => {
      if (isEditing) {
        setIsEditing(false);
      }
    };
  }, [isEditing]);

  // Force re-render when style properties change
  useEffect(() => {
    if (textRef.current) {
      textRef.current.getLayer()?.batchDraw();
    }
  }, [region.font_family, region.font_size, region.font_color, region.background_color]);

  // Listen for auto-edit event
  useEffect(() => {
    const handleAutoEdit = (event: any) => {
      if (event.detail?.regionId === region.id && !isEditing) {
        setIsEditing(true);
      }
    };

    window.addEventListener('auto-edit-region', handleAutoEdit);
    return () => {
      window.removeEventListener('auto-edit-region', handleAutoEdit);
    };
  }, [region.id, isEditing]);

  // Listen for live color preview events
  useEffect(() => {
    const handleLiveColorPreview = (event: any) => {
      const { regionIds, colorType, color } = event.detail;
      
      if (regionIds.includes(region.id)) {
        setLiveColors(prev => ({
          ...prev,
          [colorType]: color
        }));
      }
    };

    const handleColorPreviewEnd = () => {
      // Clear live colors when color dragging ends
      setLiveColors({});
    };

    window.addEventListener('live-color-preview', handleLiveColorPreview);
    window.addEventListener('color-preview-end', handleColorPreviewEnd);
    return () => {
      window.removeEventListener('live-color-preview', handleLiveColorPreview);
      window.removeEventListener('color-preview-end', handleColorPreviewEnd);
    };
  }, [region.id]);

  // Note: Canvas click outside handling is now done by InlineTextEditor
  // which properly saves the text before closing

  // Handle transform end
  const handleTransformEnd = () => {
    const node = groupRef.current;
    if (!node) return;

    const scaleX = node.scaleX();
    const scaleY = node.scaleY();

    // Reset scale and apply to width/height
    node.scaleX(1);
    node.scaleY(1);

    const updates: Partial<TextRegionResponse> = {
      x: node.x(),
      y: node.y(),
      width: Math.max(5, node.width() * scaleX),
      height: Math.max(5, node.height() * scaleY),
    };

    onTransform(updates);
  };

  // Handle drag start - prevent dragging when modifier keys are pressed for panning
  const handleDragStart = (e: any) => {
    // If modifier key is pressed, prevent text region dragging to allow stage panning
    if (e && e.evt && (e.evt.altKey || e.evt.metaKey)) {
      e.evt.preventDefault();
      e.evt.stopPropagation();
      return false;
    }
  };

  // Handle drag end
  const handleDragEnd = (e: any) => {
    const node = groupRef.current;
    if (!node) return;

    const updates: Partial<TextRegionResponse> = {
      x: node.x(),
      y: node.y(),
    };

    onTransform(updates);
  };

  // Get region color based on type
  const getRegionColor = (type: TextRegionType) => {
    switch (type) {
      case 'speech_bubble': return '#3b82f6'; // blue
      case 'thought_bubble': return '#8b5cf6'; // purple
      case 'narration': return '#10b981'; // green
      case 'sound_effect': return '#f59e0b'; // yellow
      case 'sign': return '#ef4444'; // red
      default: return '#6b7280'; // gray
    }
  };

  const regionColor = getRegionColor(region.region_type);
  const fillOpacity = isSelected ? 0.3 : (isHovered ? 0.25 : 0.2);
  const strokeWidth = isSelected ? 3 : (isHovered ? 2.5 : 2);

  // Handle double click to start editing
  const handleDoubleClick = (e?: any) => {
    // Prevent event propagation to avoid conflicts (Konva events use evt property)
    if (e && e.evt) {
      e.evt.stopPropagation();
    }

    // Only start editing if not already editing
    if (!isEditing) {
      // Ensure only this region is selected for editing
      dispatch({
        type: 'SET_SELECTED_REGIONS',
        payload: [region.id]
      });

      // Start editing immediately
      setIsEditing(true);
    }
  };

  // Handle text change from inline editor
  const handleTextChange = (newText: string) => {
    onTransform({
      translated_text: newText
    });
  };

  // Handle close inline editor
  const handleCloseEditor = () => {
    setIsEditing(false);
  };

  // Handle text region resize
  const handleResize = (newDimensions: { width: number; height: number }) => {
    onTransform({
      width: newDimensions.width,
      height: newDimensions.height
    });
  };



  return (
    <>
      <Group
        ref={groupRef}
        x={region.x}
        y={region.y}
        width={region.width}
        height={region.height}
        draggable={state.selectedTool === 'select' && !isEditing}
        onClick={(e) => {
          // Only handle single click if not in editing mode
          if (!isEditing) {
            onSelect(e);
          }
        }}
        onTap={(e) => {
          // Only handle tap if not in editing mode
          if (!isEditing) {
            onSelect(e);
          }
        }}
        onDblClick={(e) => handleDoubleClick(e)}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onTransformEnd={handleTransformEnd}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Region background */}
        <Rect
          width={region.width}
          height={region.height}
          fill={(() => {
            const bgColor = liveColors.background_color || region.background_color;
            return bgColor && bgColor !== 'transparent' ? bgColor : regionColor;
          })()}
          fillOpacity={(() => {
            const bgColor = liveColors.background_color || region.background_color;
            return bgColor && bgColor !== 'transparent' ? 0.8 : fillOpacity;
          })()}
          stroke={regionColor}
          strokeWidth={strokeWidth}
          cornerRadius={4}
          onDblClick={(e) => handleDoubleClick(e)}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        />

        {/* Original text (background, smaller, faded) */}
        {region.original_text && !region.translated_text && !isEditing && (
          <Text
            x={4}
            y={4}
            width={region.width - 8}
            height={region.height - 8}
            text={region.original_text}
            fontSize={Math.min(12, region.height / 4)}
            fill="#6b7280"
            align="center"
            verticalAlign="middle"
            listening={false}
            opacity={0.6}
          />
        )}

        {/* Main text node for editing */}
        <Text
          ref={textRef}
          x={4}
          y={4}
          width={region.width - 8}
          height={region.height - 8}
          text={region.translated_text || region.original_text || "Double-click to add text"}
          fontSize={region.font_size || Math.min(14, region.height / 3)}
          fontFamily={region.font_family || 'Arial'}
          fill={region.translated_text ? (liveColors.font_color || region.font_color || '#000000') : '#9ca3af'}
          align="center"
          verticalAlign="middle"
          listening={true}
          visible={!isEditing}
          opacity={region.translated_text ? 1 : 0.7}
          fontStyle={region.translated_text ? 'normal' : 'italic'}
          onDblClick={(e) => handleDoubleClick(e)}
        />

        {/* Region type indicator */}
        <Rect
          x={0}
          y={0}
          width={20}
          height={16}
          fill={regionColor}
          cornerRadius={[4, 0, 4, 0]}
        />
        <Text
          x={2}
          y={2}
          width={16}
          height={12}
          text={region.region_type.charAt(0).toUpperCase()}
          fontSize={10}
          fill="white"
          align="center"
          verticalAlign="middle"
          listening={false}
          fontStyle="bold"
        />
      </Group>

      {/* Inline Text Editor */}
      {isEditing && textRef.current && (
        <InlineTextEditor
          key={`editor-${region.id}`}
          textNode={textRef.current}
          region={region}
          initialText={region.translated_text || region.original_text || ""}
          onChange={handleTextChange}
          onClose={handleCloseEditor}
          onResize={handleResize}
        />
      )}

      {/* Transformer for selected regions */}
      {isSelected && !isEditing && (
        <Transformer
          ref={transformerRef}
          boundBoxFunc={(oldBox, newBox) => {
            // Limit resize
            if (newBox.width < 5 || newBox.height < 5) {
              return oldBox;
            }
            return newBox;
          }}
          enabledAnchors={[
            'top-left',
            'top-right',
            'bottom-left',
            'bottom-right',
            'middle-left',
            'middle-right',
            'top-center',
            'bottom-center',
          ]}
        />
      )}
    </>
  );
}
